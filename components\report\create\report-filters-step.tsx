"use client";

import React, { useEffect } from "react";
import { Card, CardBody, CardHeader, Select, SelectItem } from "@heroui/react";
import { Icon } from "@iconify/react";

import { useAllFieldsQuery } from "@/graphql/schemas/generated";

interface ReportFiltersStepProps {
  selectedFields: string[];
  fieldFilters: Record<string, "completed" | "not_completed" | "in_progress">;
  onFiltersChange: (
    filters: Record<string, "completed" | "not_completed" | "in_progress">,
  ) => void;
}

// Filter options for each field type
const getFilterOptionsForType = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return [
        {
          key: "completed",
          label: "Completado",
          color: "success" as const,
          dotColor: "bg-success"
        },
        {
          key: "not_completed",
          label: "No completado",
          color: "danger" as const,
          dotColor: "bg-danger"
        },
        {
          key: "in_progress",
          label: "En progreso",
          color: "warning" as const,
          dotColor: "bg-warning"
        },
      ];
    case "selection":
      return [
        {
          key: "completed",
          label: "Completado",
          color: "success" as const,
          dotColor: "bg-success"
        },
        {
          key: "not_completed",
          label: "No completado",
          color: "danger" as const,
          dotColor: "bg-danger"
        },
        {
          key: "in_progress",
          label: "En progreso",
          color: "warning" as const,
          dotColor: "bg-warning"
        },
      ];
    case "task":
    case "document":
    case "task_with_subtasks":
      return [
        {
          key: "completed",
          label: "Completado",
          color: "success" as const,
          dotColor: "bg-success"
        },
        {
          key: "not_completed",
          label: "No completado",
          color: "danger" as const,
          dotColor: "bg-danger"
        },
        {
          key: "in_progress",
          label: "En progreso",
          color: "warning" as const,
          dotColor: "bg-warning"
        },
      ];
    default:
      return [
        {
          key: "completed",
          label: "Completado",
          color: "success" as const,
          dotColor: "bg-success"
        },
        {
          key: "not_completed",
          label: "No completado",
          color: "danger" as const,
          dotColor: "bg-danger"
        },
        {
          key: "in_progress",
          label: "En progreso",
          color: "warning" as const,
          dotColor: "bg-warning"
        },
      ];
  }
};

const getFieldTypeDisplayName = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return "Informativo";
    case "selection":
      return "Selección";
    case "task":
      return "Tarea";
    case "document":
      return "Documento";
    case "task_with_subtasks":
      return "Subtarea";
    default:
      return fieldType;
  }
};

const getFieldTypeIcon = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return "heroicons:information-circle";
    case "selection":
      return "heroicons:list-bullet";
    case "task":
      return "heroicons:check-circle";
    case "document":
      return "heroicons:document";
    case "task_with_subtasks":
      return "heroicons:squares-plus";
    default:
      return "heroicons:question-mark-circle";
  }
};

export default function ReportFiltersStep({
  selectedFields,
  fieldFilters,
  onFiltersChange,
}: ReportFiltersStepProps) {
  const { data: fieldsData } = useAllFieldsQuery();

  const fields = fieldsData?.allFields || [];

  // Get selected field details
  const selectedFieldsData = fields.filter((field) =>
    selectedFields.includes(field?.id || ""),
  );

  // Get unique field types from selected fields
  const selectedFieldTypes = Array.from(
    new Set(selectedFieldsData.map((field) => field?.type).filter(Boolean)),
  );

  // Initialize filters for field types
  useEffect(() => {
    const newFilters: Record<
      string,
      "completed" | "not_completed" | "in_progress"
    > = {};

    selectedFieldTypes.forEach((fieldType) => {
      if (fieldType && !fieldFilters[fieldType]) {
        newFilters[fieldType] = "completed";
      }
    });

    // Only update if there are new field types
    if (Object.keys(newFilters).length > 0) {
      onFiltersChange({ ...fieldFilters, ...newFilters });
    }
  }, [selectedFieldTypes]);

  const handleFilterChange = (
    fieldType: string,
    filterOption: "completed" | "not_completed" | "in_progress",
  ) => {
    onFiltersChange({
      ...fieldFilters,
      [fieldType]: filterOption,
    });
  };

  // Group fields by type
  const fieldsByType = selectedFieldsData.reduce(
    (acc, field) => {
      const type = field?.type || "unknown";

      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(field);

      return acc;
    },
    {} as Record<string, any[]>,
  );

  if (selectedFields.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <Icon
          className="text-warning mb-4"
          icon="heroicons:exclamation-triangle"
          width={48}
        />
        <h3 className="text-lg font-semibold mb-2">
          No hay campos seleccionados
        </h3>
        <p className="text-default-500">
          Regresa al paso anterior para seleccionar los campos que deseas
          incluir en el reporte.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-semibold mb-2">
          Configurar filtros por tipo de campo
        </h3>
        <p className="text-default-500">
          Define cómo filtrar los datos para cada tipo de campo seleccionado
        </p>
      </div>

      {Object.entries(fieldsByType).map(([fieldType, fieldsOfType]) => {
        const filterOptions = getFilterOptionsForType(fieldType);
        const currentFilter = fieldFilters[fieldType] || "completed";

        return (
          <Card key={fieldType} className="w-full">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Icon
                    className="text-primary"
                    icon={getFieldTypeIcon(fieldType)}
                    width={24}
                  />
                  <div>
                    <h4 className="text-lg font-semibold">
                      {getFieldTypeDisplayName(fieldType)}
                    </h4>
                    <p className="text-sm text-default-500">
                      {fieldsOfType.length} campo(s) de este tipo
                    </p>
                  </div>
                </div>
                <div className="min-w-[250px]">
                  <Select
                    placeholder="Seleccionar filtro"
                    selectedKeys={[currentFilter]}
                    size="sm"
                    onSelectionChange={(keys) => {
                      const selectedKey = Array.from(keys)[0] as
                        | "completed"
                        | "not_completed"
                        | "in_progress";

                      if (selectedKey) {
                        handleFilterChange(fieldType, selectedKey);
                      }
                    }}
                  >
                    {filterOptions.map((option) => (
                      <SelectItem key={option.key}>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${option.dotColor}`} />
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardBody className="pt-0">
              <div className="space-y-2">
                <p className="text-sm text-default-600 mb-3">
                  Campos incluidos en este tipo:
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {fieldsOfType.map((field) => (
                    <div
                      key={field?.id}
                      className="flex items-center gap-2 p-2 bg-default-50 rounded-lg"
                    >
                      <div className="flex-1">
                        <div className="font-medium text-sm">{field?.name}</div>
                        <div className="text-xs text-default-500">
                          {field?.subphase?.phase?.name} -{" "}
                          {field?.subphase?.name}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardBody>
          </Card>
        );
      })}
    </div>
  );
}
