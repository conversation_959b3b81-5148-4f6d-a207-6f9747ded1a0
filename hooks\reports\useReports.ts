"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";
import { ApiReport, CreateReportData, UpdateReportData } from "@/types/report";

export function useReports() {
  const [reports, setReports] = useState<ApiReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(API_ROUTES.ALL_REPORTS);

      if (response.status === 200) {
        setReports(response.data);
      } else {
        setError("Failed to fetch reports");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch reports");
    } finally {
      setLoading(false);
    }
  };

  const createReport = async (
    reportData: CreateReportData,
  ): Promise<{ success: boolean; data?: ApiReport; error?: string }> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axiosInstance.post(
        API_ROUTES.CREATE_REPORT,
        reportData,
      );

      if (response.status === 201) {
        // Update reports state with the new report
        setReports((prevReports) => [...prevReports, response.data]);
        return { success: true, data: response.data };
      } else {
        const errorMsg = "Failed to create report";
        setError(errorMsg);
        return { success: false, error: errorMsg };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to create report";
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const updateReport = async (
    id: number,
    updateData: UpdateReportData,
  ): Promise<{ success: boolean; data?: ApiReport; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.put(
        API_ROUTES.UPDATE_REPORT.replace("{id}", id.toString()),
        updateData,
      );

      if (response.status === 200) {
        // Update reports state
        setReports((prevReports) =>
          prevReports.map((report) =>
            report.id === id ? { ...report, ...response.data } : report,
          ),
        );
        return { success: true, data: response.data };
      } else {
        const errorMsg = "Failed to update report";
        setError(errorMsg);
        return { success: false, error: errorMsg };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to update report";
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const deleteReport = async (
    id: number,
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.delete(
        API_ROUTES.DELETE_REPORT.replace("{id}", id.toString()),
      );

      if (response.status === 200 || response.status === 204) {
        // Remove report from state
        setReports((prevReports) =>
          prevReports.filter((report) => report.id !== id),
        );
        return { success: true };
      } else {
        const errorMsg = "Failed to delete report";
        setError(errorMsg);
        return { success: false, error: errorMsg };
      }
    } catch (err: any) {
      const errorMsg = err.message || "Failed to delete report";
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    reports,
    loading,
    error,
    fetchReports,
    createReport,
    updateReport,
    deleteReport,
  };
}
