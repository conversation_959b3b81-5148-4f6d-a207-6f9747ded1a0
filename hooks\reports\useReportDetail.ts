"use client";

import { useState } from "react";

import { API_ROUTES } from "@/lib/api";
import { axiosInstance } from "@/lib/axios";
import { ReportDetailResponse } from "@/types/report";

export function useReportDetail() {
  const [reportDetail, setReportDetail] = useState<ReportDetailResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchReportDetail = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axiosInstance.get(`${API_ROUTES.REPORT_DETAIL}${id}/`);

      if (response.status === 200) {
        setReportDetail(response.data);
        return response.data;
      } else {
        setError("Failed to fetch report detail");
      }
    } catch (err: any) {
      setError(err.message || "Failed to fetch report detail");
    } finally {
      setLoading(false);
    }
  };

  const clearReportDetail = () => {
    setReportDetail(null);
    setError(null);
  };

  return {
    reportDetail,
    loading,
    error,
    fetchReportDetail,
    clearReportDetail,
  };
}
